'use client'

import { useMutation, useQuery } from '@tanstack/react-query'
import { Link, Shield, Unlink } from 'lucide-react'

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import { queryFetchHelper } from '@/lib/utils/fetchHelper'
import { toast } from 'sonner'

// API Response interface
interface ApiResponse<T = any> {
  success: boolean
  code: number
  locale: string
  message: string
  data?: T
}

// Social account interface
interface SocialAccount {
  id: string
  provider: string
  name: string
  avatar?: string
  account_id: string
  created_at: string
}

// Google SVG Icon component
const GoogleIcon = () => (
  <svg
    viewBox="0 0 24 24"
    className="h-5 w-5">
    <path
      fill="#4285F4"
      d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
    />
    <path
      fill="#34A853"
      d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
    />
    <path
      fill="#FBBC05"
      d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
    />
    <path
      fill="#EA4335"
      d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
    />
  </svg>
)

// Social providers configuration - Only Google supported
const socialProviders = [
  {
    id: 'google',
    name: 'Google',
    icon: GoogleIcon,
    color: 'bg-white border',
  },
] as const

export default function SocialLoginSection() {
  // Fetch user's social accounts
  const {
    data: socialAccounts,
    refetch: refetchSocialAccounts,
    isLoading,
    error,
  } = useQuery<ApiResponse<{ items: SocialAccount[] }>>({
    queryKey: ['socialAccounts'],
    queryFn: () => queryFetchHelper('/profile/social-accounts'),
  })

  // Social account disconnect mutation
  const { isPending: isDisconnecting, mutate: disconnectSocialAccount } = useMutation<ApiResponse, Error, string>({
    mutationFn: async (accountId: string) => {
      return queryFetchHelper(`/profile/social-accounts/${accountId}`, {
        method: 'DELETE',
      })
    },
    onSuccess: (data) => {
      toast.success(data.message || 'Tài khoản đã được ngắt kết nối')
      refetchSocialAccounts()
    },
    onError: (error) => {
      toast.error(error.message || 'Ngắt kết nối thất bại')
    },
  })

  const handleDisconnectSocialAccount = (accountId: string): void => {
    disconnectSocialAccount(accountId)
  }

  return (
    <div className="space-y-4">
      <div>
        <h2 className="flex items-center gap-2 text-lg font-semibold">
          <Shield className="h-5 w-5" />
          Quản lý đăng nhập xã hội
        </h2>
        <p className="text-muted-foreground text-sm">Kết nối hoặc ngắt kết nối các tài khoản mạng xã hội của bạn</p>
      </div>
      <div>
        {isLoading ? (
          <div className="text-muted-foreground py-8 text-center">
            <Shield className="mx-auto mb-4 h-12 w-12 animate-pulse opacity-50" />
            <p>Đang tải...</p>
          </div>
        ) : error ? (
          <div className="text-muted-foreground py-8 text-center">
            <Shield className="mx-auto mb-4 h-12 w-12 opacity-50" />
            <p>Không thể tải thông tin tài khoản</p>
            <p className="text-sm">Vui lòng thử lại sau</p>
          </div>
        ) : (
          <div className="space-y-4">
            {socialProviders.map((provider) => {
              const connectedAccount = Array.isArray(socialAccounts?.data?.items)
                ? socialAccounts.data.items.find((account) => account.provider === provider.id)
                : undefined

              return (
                <div
                  key={provider.id}
                  className="flex items-center justify-between rounded-lg border p-4">
                  <div className="flex items-center gap-3">
                    <div className={cn('flex h-10 w-10 items-center justify-center rounded-full', provider.color)}>
                      <provider.icon />
                    </div>
                    <div>
                      <h4 className="font-medium">{provider.name}</h4>
                      {connectedAccount ? (
                        <p className="text-muted-foreground text-sm">Đã kết nối với {connectedAccount.name}</p>
                      ) : (
                        <p className="text-muted-foreground text-sm">Chưa kết nối</p>
                      )}
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    {connectedAccount ? (
                      <>
                        <Badge
                          variant="secondary"
                          className="flex h-8 items-center gap-1">
                          <Link className="h-3 w-3" />
                          Đã kết nối
                        </Badge>
                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <Button
                              variant="outline"
                              size="sm"
                              className="h-8 px-3"
                              disabled={isDisconnecting}>
                              <Unlink className="mr-1 h-3 w-3" />
                              Ngắt kết nối
                            </Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>Xác nhận ngắt kết nối</AlertDialogTitle>
                              <AlertDialogDescription>
                                Bạn có chắc chắn muốn ngắt kết nối tài khoản {provider.name} này không? Bạn sẽ không thể
                                đăng nhập bằng {provider.name} nữa.
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>Hủy</AlertDialogCancel>
                              <AlertDialogAction
                                onClick={() => handleDisconnectSocialAccount(connectedAccount.id)}
                                className="bg-destructive text-destructive-foreground hover:bg-destructive/90">
                                Ngắt kết nối
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </>
                    ) : (
                      <Button
                        variant="outline"
                        size="sm"
                        className="h-8 px-3">
                        <Link className="mr-1 h-3 w-3" />
                        Kết nối
                      </Button>
                    )}
                  </div>
                </div>
              )
            })}

            {(!Array.isArray(socialAccounts?.data?.items) || socialAccounts.data.items.length === 0) && (
              <div className="text-muted-foreground py-8 text-center">
                <Shield className="mx-auto mb-4 h-12 w-12 opacity-50" />
                <p>Không tìm thấy tài khoản nào</p>
                <p className="text-sm">Kết nối tài khoản Google để đăng nhập dễ dàng hơn</p>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  )
}
