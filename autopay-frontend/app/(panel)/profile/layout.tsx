import { Metadata } from 'next'

import PageHeading from '@/app/(panel)/common/components/page-heading'
import { Card, CardContent } from '@/components/ui/card'
import React from 'react'
import { SidebarNav } from './components/sidebar-nav'

export const metadata: Metadata = {
  title: 'Forms',
  description: 'Advanced form example using react-hook-form and Zod.',
}

const sidebarNavItems = [
  {
    title: 'Thông tin',
    href: '/profile',
  },
  {
    title: 'Bảo mật',
    href: '/profile/security',
  },
  {
    title: 'Hiển thị',
    href: '/profile/appearance',
  },
  {
    title: 'Thông báo',
    href: '/profile/notifications',
  },
]

interface SettingsLayoutProps {
  children: React.ReactNode
}

export default function SettingsLayout({ children }: SettingsLayoutProps) {
  return (
    <>
      <PageHeading
        title="Thông tin tài khoản"
        description="Cập nhật thông tin phù hợp với tài khoản của bạn."
        withSeparator={true}
      />
      <div className="flex flex-col space-y-8 lg:flex-row lg:space-y-0 lg:space-x-5">
        <aside className="md:-ml-1 md:w-52">
          <SidebarNav items={sidebarNavItems} />
        </aside>
        <Card className="flex-1 py-0">
          <CardContent className="p-6">{children}</CardContent>
        </Card>
      </div>
    </>
  )
}
