<?php

namespace Modules\User\Http\Requests;

use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules\Password;
use Modules\Core\Http\Requests\BaseFormRequest;

class UpdatePasswordRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'password' => [
                'required',
                'min:6',
                'max:32',
                function ($attribute, $value, $fail): void {
                    if (! Hash::check($value, $this->user()->password)) {
                        $fail('Giá trị '.$attribute.' hiện tại không chính xác.');
                    }
                },
            ],
            'new_password' => [
                'required',
                'confirmed',
                Password::min(8)
                    ->letters()
                    ->mixedCase()
                    ->numbers()
                    ->symbols()
                    ->uncompromised(),
            ],
            'new_password_confirmation' => [
                'required',
                'same:new_password',
            ],
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }
}
